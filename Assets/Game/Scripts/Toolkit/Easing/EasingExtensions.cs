using UncleChenGames.Extensions;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.UI;
using UncleChenGames.Easing.Pool;

namespace UncleChenGames.Easing
{
    public static class EasingExtensions
    {
        public static IEasingCoroutine DoAnchorPosition(this RectTransform rect, Vector2 targetPosition, float duration, float delay = 0)
        {
            Vector3 startPos = rect.anchoredPosition3D;
            Vector3 targetPos = new Vector3(targetPosition.x, targetPosition.y, startPos.z);

            // 根据设置选择使用对象池或直接创建
            if (EasingManager.UseObjectPool)
            {
                return EasingPoolManager.GetVector3Coroutine(startPos, targetPos, duration, delay, (position) => rect.anchoredPosition3D = position);
            }
            else
            {
                return new VectorEasingCoroutine3(startPos, targetPos, duration, delay, (position) => rect.anchoredPosition3D = position);
            }
        }

        public static IEasingCoroutine DoSizeDelta(this RectTransform rect, Vector2 targetSizeDelta, float duration, float delay = 0)
        {
            Vector2 startSizeDelta = rect.sizeDelta;

            // 根据设置选择使用对象池或直接创建
            if (EasingManager.UseObjectPool)
            {
                return EasingPoolManager.GetVector2Coroutine(startSizeDelta, targetSizeDelta, duration, delay, (position) => rect.sizeDelta = position);
            }
            else
            {
                return new VectorEasingCoroutine2(startSizeDelta, targetSizeDelta, duration, delay, (position) => rect.sizeDelta = position);
            }
        }

        public static IEasingCoroutine DoPosition(this Transform transform, Vector3 targetPosition, float duration, float delay = 0)
        {
            Vector3 startPosition = transform.position;

            // 根据设置选择使用对象池或直接创建
            if (EasingManager.UseObjectPool)
            {
                return EasingPoolManager.GetVector3Coroutine(startPosition, targetPosition, duration, delay, (position) => transform.position = position);
            }
            else
            {
                return new VectorEasingCoroutine3(startPosition, targetPosition, duration, delay, (position) => transform.position = position);
            }
        }

        public static IEasingCoroutine DoPosition(this Transform transform, Transform targetTransform, float duration, float delay = 0)
        {
            Vector3 startPosition = transform.position;

            IEasingCoroutine coroutine = EasingManager.DoFloat(0, 1, duration, (value) =>
            {
                transform.position = Vector3.LerpUnclamped(startPosition, targetTransform.position, value);
            }, delay);

            return coroutine;
        }

        public static IEasingCoroutine DoLocalScale(this Transform transform, Vector3 targetScale, float duration, float delay = 0)
        {
            Vector3 startScale = transform.localScale;

            // 根据设置选择使用对象池或直接创建
            if (EasingManager.UseObjectPool)
            {
                return EasingPoolManager.GetVector3Coroutine(startScale, targetScale, duration, delay, (scale) => transform.localScale = scale);
            }
            else
            {
                return new VectorEasingCoroutine3(startScale, targetScale, duration, delay, (scale) => transform.localScale = scale);
            }
        }

        public static IEasingCoroutine DoRotation(this Transform transform, Quaternion targetRotation, float duration, float delay = 0)
        {
            Quaternion startRotation = transform.rotation;

            return EasingManager.DoFloat(0, 1, duration, (t) =>
            {
                transform.rotation = Quaternion.SlerpUnclamped(startRotation, targetRotation, t);
            }, delay);
        }

        public static IEasingCoroutine DoLocalRotation(this Transform transform, Quaternion targetRotation, float duration, float delay = 0)
        {
            Quaternion startRotation = transform.localRotation;

            return EasingManager.DoFloat(0, 1, duration, (t) =>
            {
                transform.localRotation = Quaternion.SlerpUnclamped(startRotation, targetRotation, t);
            }, delay);
        }

        public static void StopIfExists(this IEasingCoroutine coroutine)
        {
            if (coroutine != null && coroutine.IsActive) coroutine.Stop();
        }

        public static bool ExistsAndActive(this IEasingCoroutine coroutine)
        {
            return coroutine != null && coroutine.IsActive;
        }

        public static IEasingCoroutine DoColor(this MaterialPropertyBlock block, string property, Color color, float duration, float delay = 0)
        {
            var id = Shader.PropertyToID(property);

            var startColor = block.GetColor(id);

            return new ColorEasingCoroutine(startColor, color, duration, delay, (color) => block.SetColor(id, color));
        }

        public static IEasingCoroutine DoColor(this Material material, string property, Color color, float duration, float delay = 0)
        {
            var id = Shader.PropertyToID(property);
            var startColor = material.GetColor(id);

            // 根据设置选择使用对象池或直接创建
            if (EasingManager.UseObjectPool)
            {
                return EasingPoolManager.GetColorCoroutine(startColor, color, duration, delay, (color) => material.SetColor(id, color));
            }
            else
            {
                return new ColorEasingCoroutine(startColor, color, duration, delay, (color) => material.SetColor(id, color));
            }
        }

        public static IEasingCoroutine DoColor(this Material material, int propertyId, Color color, float duration, float delay = 0)
        {
            var startColor = material.GetColor(propertyId);

            // 根据设置选择使用对象池或直接创建
            if (EasingManager.UseObjectPool)
            {
                return EasingPoolManager.GetColorCoroutine(startColor, color, duration, delay, (color) => material.SetColor(propertyId, color));
            }
            else
            {
                return new ColorEasingCoroutine(startColor, color, duration, delay, (color) => material.SetColor(propertyId, color));
            }
        }

        public static IEasingCoroutine DoFloat(this Material material, string property, float value, float duration, float delay = 0)
        {
            var id = Shader.PropertyToID(property);
            var startValue = material.GetFloat(id);

            // 根据设置选择使用对象池或直接创建
            if (EasingManager.UseObjectPool)
            {
                return EasingPoolManager.GetFloatCoroutine(startValue, value, duration, delay, (value) => material.SetFloat(id, value));
            }
            else
            {
                return new FloatEasingCoroutine(startValue, value, duration, delay, (value) => material.SetFloat(id, value));
            }
        }

        public static IEasingCoroutine DoFloat(this Material material, int propertyId, float value, float duration, float delay = 0)
        {
            var startValue = material.GetFloat(propertyId);

            // 根据设置选择使用对象池或直接创建
            if (EasingManager.UseObjectPool)
            {
                return EasingPoolManager.GetFloatCoroutine(startValue, value, duration, delay, (value) => material.SetFloat(propertyId, value));
            }
            else
            {
                return new FloatEasingCoroutine(startValue, value, duration, delay, (value) => material.SetFloat(propertyId, value));
            }
        }

        public static IEasingCoroutine DoAlpha(this Graphic graphic, float targetAlpha, float duration, float delay = 0)
        {
            var initialAlpha = graphic.color.a;

            // 根据设置选择使用对象池或直接创建
            if (EasingManager.UseObjectPool)
            {
                return EasingPoolManager.GetFloatCoroutine(initialAlpha, targetAlpha, duration, delay, (alpha) => graphic.SetAlpha(alpha));
            }
            else
            {
                return new FloatEasingCoroutine(initialAlpha, targetAlpha, duration, delay, (alpha) => graphic.SetAlpha(alpha));
            }
        }

        public static IEasingCoroutine DoAlpha(this CanvasGroup canvasGroup, float targetAlpha, float duration, float delay = 0)
        {
            var initialAlpha = canvasGroup.alpha;

            // 根据设置选择使用对象池或直接创建
            if (EasingManager.UseObjectPool)
            {
                return EasingPoolManager.GetFloatCoroutine(initialAlpha, targetAlpha, duration, delay, (alpha) => canvasGroup.alpha = alpha);
            }
            else
            {
                return new FloatEasingCoroutine(initialAlpha, targetAlpha, duration, delay, (alpha) => canvasGroup.alpha = alpha);
            }
        }

        public static IEasingCoroutine DoAlpha(this SpriteRenderer spriteRenderer, float targetAlpha, float duration, float delay = 0)
        {
            var initialAlpha = spriteRenderer.color.a;

            // 根据设置选择使用对象池或直接创建
            if (EasingManager.UseObjectPool)
            {
                return EasingPoolManager.GetFloatCoroutine(initialAlpha, targetAlpha, duration, delay, (alpha) => spriteRenderer.SetAlpha(alpha));
            }
            else
            {
                return new FloatEasingCoroutine(initialAlpha, targetAlpha, duration, delay, (alpha) => spriteRenderer.SetAlpha(alpha));
            }
        }

        public static IEasingCoroutine DoVolume(this AudioSource audioSource, float targetVolume, float duration, float delay = 0)
        {
            var initialVolume = audioSource.volume;

            // 根据设置选择使用对象池或直接创建
            if (EasingManager.UseObjectPool)
            {
                return EasingPoolManager.GetFloatCoroutine(initialVolume, targetVolume, duration, delay, (volume) => audioSource.volume = volume);
            }
            else
            {
                return new FloatEasingCoroutine(initialVolume, targetVolume, duration, delay, (volume) => audioSource.volume = volume);
            }
        }
    }
}