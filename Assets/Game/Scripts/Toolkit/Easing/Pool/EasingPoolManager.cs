using UnityEngine;
using UnityEngine.Events;

namespace UncleChenGames.Easing.Pool
{
    /// <summary>
    /// 缓动对象池管理器
    /// 管理所有类型的缓动协程对象池，提供统一的获取接口
    /// </summary>
    public class EasingPoolManager : MonoBehaviour
    {
        private static EasingPoolManager instance; // 单例实例

        [Header("对象池配置")]
        [SerializeField] private int floatPoolSize = 20; // 浮点数池大小
        [SerializeField] private int vector3PoolSize = 30; // Vector3池大小
        [SerializeField] private int vector2PoolSize = 20; // Vector2池大小
        [SerializeField] private int colorPoolSize = 15; // Color池大小

        // 各类型对象池
        private FloatEasingCoroutinePool floatPool;
        private Vector3EasingCoroutinePool vector3Pool;
        private Vector2EasingCoroutinePool vector2Pool;
        private ColorEasingCoroutinePool colorPool;

        /// <summary>
        /// 获取单例实例
        /// </summary>
        public static EasingPoolManager Instance
        {
            get
            {
                if (instance == null)
                {
                    // 尝试在场景中查找
                    instance = FindObjectOfType<EasingPoolManager>();
                    
                    if (instance == null)
                    {
                        // 创建新的管理器对象
                        GameObject go = new GameObject("EasingPoolManager");
                        instance = go.AddComponent<EasingPoolManager>();
                        DontDestroyOnLoad(go);
                    }
                }
                return instance;
            }
        }

        /// <summary>
        /// 初始化对象池管理器
        /// </summary>
        private void Awake()
        {
            // 单例检查
            if (instance != null && instance != this)
            {
                Debug.LogError("检测到多个 EasingPoolManager 实例！正在销毁重复的实例。");
                Destroy(gameObject);
                return;
            }

            instance = this;
            DontDestroyOnLoad(gameObject);

            // 初始化所有对象池
            InitializePools();
        }

        /// <summary>
        /// 初始化所有对象池
        /// </summary>
        private void InitializePools()
        {
            floatPool = new FloatEasingCoroutinePool(floatPoolSize);
            vector3Pool = new Vector3EasingCoroutinePool(vector3PoolSize);
            vector2Pool = new Vector2EasingCoroutinePool(vector2PoolSize);
            colorPool = new ColorEasingCoroutinePool(colorPoolSize);

            Debug.Log($"EasingPoolManager 初始化完成 - Float:{floatPoolSize}, Vector3:{vector3PoolSize}, Vector2:{vector2PoolSize}, Color:{colorPoolSize}");
        }

        /// <summary>
        /// 获取浮点数缓动协程
        /// </summary>
        /// <param name="from">起始值</param>
        /// <param name="to">目标值</param>
        /// <param name="duration">持续时间</param>
        /// <param name="delay">延迟时间</param>
        /// <param name="callback">值更新回调</param>
        /// <returns>缓动协程</returns>
        public static IEasingCoroutine GetFloatCoroutine(float from, float to, float duration, float delay, UnityAction<float> callback)
        {
            return Instance.floatPool.GetCoroutine(from, to, duration, delay, callback);
        }

        /// <summary>
        /// 获取 Vector3 缓动协程
        /// </summary>
        /// <param name="from">起始值</param>
        /// <param name="to">目标值</param>
        /// <param name="duration">持续时间</param>
        /// <param name="delay">延迟时间</param>
        /// <param name="callback">值更新回调</param>
        /// <returns>缓动协程</returns>
        public static IEasingCoroutine GetVector3Coroutine(Vector3 from, Vector3 to, float duration, float delay, UnityAction<Vector3> callback)
        {
            return Instance.vector3Pool.GetCoroutine(from, to, duration, delay, callback);
        }

        /// <summary>
        /// 获取 Vector2 缓动协程
        /// </summary>
        /// <param name="from">起始值</param>
        /// <param name="to">目标值</param>
        /// <param name="duration">持续时间</param>
        /// <param name="delay">延迟时间</param>
        /// <param name="callback">值更新回调</param>
        /// <returns>缓动协程</returns>
        public static IEasingCoroutine GetVector2Coroutine(Vector2 from, Vector2 to, float duration, float delay, UnityAction<Vector2> callback)
        {
            return Instance.vector2Pool.GetCoroutine(from, to, duration, delay, callback);
        }

        /// <summary>
        /// 获取 Color 缓动协程
        /// </summary>
        /// <param name="from">起始值</param>
        /// <param name="to">目标值</param>
        /// <param name="duration">持续时间</param>
        /// <param name="delay">延迟时间</param>
        /// <param name="callback">值更新回调</param>
        /// <returns>缓动协程</returns>
        public static IEasingCoroutine GetColorCoroutine(Color from, Color to, float duration, float delay, UnityAction<Color> callback)
        {
            return Instance.colorPool.GetCoroutine(from, to, duration, delay, callback);
        }

        /// <summary>
        /// 获取对象池统计信息
        /// </summary>
        /// <returns>统计信息字符串</returns>
        public string GetPoolStats()
        {
            if (floatPool == null) return "对象池未初始化";

            return $"EasingPool Stats:\n" +
                   $"Float Pool: Active/Total\n" +
                   $"Vector3 Pool: Active/Total\n" +
                   $"Vector2 Pool: Active/Total\n" +
                   $"Color Pool: Active/Total";
        }

        /// <summary>
        /// 清理所有对象池
        /// </summary>
        public void ClearAllPools()
        {
            floatPool = null;
            vector3Pool = null;
            vector2Pool = null;
            colorPool = null;
        }

        /// <summary>
        /// 销毁时清理
        /// </summary>
        private void OnDestroy()
        {
            if (instance == this)
            {
                ClearAllPools();
                instance = null;
            }
        }

        /// <summary>
        /// 应用程序退出时清理
        /// </summary>
        private void OnApplicationQuit()
        {
            ClearAllPools();
        }

#if UNITY_EDITOR
        /// <summary>
        /// 编辑器下显示对象池状态
        /// </summary>
        private void OnGUI()
        {
            if (Application.isPlaying && instance == this)
            {
                GUILayout.BeginArea(new Rect(10, 10, 300, 150));
                GUILayout.Label("Easing Pool Manager", EditorStyles.boldLabel);
                GUILayout.Label(GetPoolStats());
                GUILayout.EndArea();
            }
        }

        private static class EditorStyles
        {
            public static GUIStyle boldLabel = new GUIStyle(GUI.skin.label) { fontStyle = FontStyle.Bold };
        }
#endif
    }
}
