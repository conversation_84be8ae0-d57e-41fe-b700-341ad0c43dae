using UnityEngine;
using UnityEngine.UI;
using System.Collections;

namespace UncleChenGames.Easing.Pool
{
    /// <summary>
    /// 缓动对象池兼容性测试
    /// 验证对象池实现不会破坏现有 API 的使用方式
    /// </summary>
    public class EasingPoolCompatibilityTest : MonoBehaviour
    {
        [Header("测试对象")]
        [SerializeField] private Transform testTransform; // 测试用的 Transform
        [SerializeField] private RectTransform testRectTransform; // 测试用的 RectTransform
        [SerializeField] private Material testMaterial; // 测试用的 Material
        [SerializeField] private Graphic testGraphic; // 测试用的 UI Graphic
        [SerializeField] private CanvasGroup testCanvasGroup; // 测试用的 CanvasGroup
        [SerializeField] private SpriteRenderer testSpriteRenderer; // 测试用的 SpriteRenderer
        [SerializeField] private AudioSource testAudioSource; // 测试用的 AudioSource

        [Header("测试设置")]
        [SerializeField] private bool runTestsOnStart = true; // 是否在开始时运行测试
        [SerializeField] private bool enablePooling = true; // 是否启用对象池
        [SerializeField] private float testDuration = 1f; // 测试动画持续时间

        private int completedTests = 0; // 已完成的测试数量
        private int totalTests = 0; // 总测试数量

        /// <summary>
        /// 开始时运行测试
        /// </summary>
        private void Start()
        {
            if (runTestsOnStart)
            {
                StartCoroutine(RunAllCompatibilityTests());
            }
        }

        /// <summary>
        /// 运行所有兼容性测试
        /// </summary>
        /// <returns>测试协程</returns>
        public IEnumerator RunAllCompatibilityTests()
        {
            Debug.Log("=== 开始缓动对象池兼容性测试 ===");
            
            // 设置对象池状态
            if (EasingManager.Instance != null)
            {
                // 这里需要通过反射或其他方式设置 useObjectPool 字段
                Debug.Log($"对象池状态: {(enablePooling ? "启用" : "禁用")}");
            }

            completedTests = 0;
            totalTests = 15; // 总测试数量

            // 验证池管理器状态
            var poolValidation = EasingPoolValidator.ValidatePoolManager();
            Debug.Log(poolValidation.GetReport());

            // Transform 相关测试
            yield return StartCoroutine(TestTransformAnimations());

            // RectTransform 相关测试
            yield return StartCoroutine(TestRectTransformAnimations());

            // Material 相关测试
            yield return StartCoroutine(TestMaterialAnimations());

            // UI 相关测试
            yield return StartCoroutine(TestUIAnimations());

            // Audio 相关测试
            yield return StartCoroutine(TestAudioAnimations());

            // 链式调用测试
            yield return StartCoroutine(TestChainedCalls());

            // 序列动画测试
            yield return StartCoroutine(TestSequenceAnimations());

            Debug.Log($"=== 兼容性测试完成: {completedTests}/{totalTests} ===");
        }

        /// <summary>
        /// 测试 Transform 动画
        /// </summary>
        private IEnumerator TestTransformAnimations()
        {
            if (testTransform == null) yield break;

            Debug.Log("测试 Transform 动画...");

            // 测试位置动画
            var positionTween = testTransform.DoPosition(Vector3.up, testDuration);
            yield return new WaitForSeconds(testDuration + 0.1f);
            LogTestResult("DoPosition", positionTween != null);

            // 测试缩放动画
            var scaleTween = testTransform.DoLocalScale(Vector3.one * 1.5f, testDuration);
            yield return new WaitForSeconds(testDuration + 0.1f);
            LogTestResult("DoLocalScale", scaleTween != null);

            // 测试旋转动画
            var rotationTween = testTransform.DoRotation(Quaternion.Euler(0, 90, 0), testDuration);
            yield return new WaitForSeconds(testDuration + 0.1f);
            LogTestResult("DoRotation", rotationTween != null);
        }

        /// <summary>
        /// 测试 RectTransform 动画
        /// </summary>
        private IEnumerator TestRectTransformAnimations()
        {
            if (testRectTransform == null) yield break;

            Debug.Log("测试 RectTransform 动画...");

            // 测试锚点位置动画
            var anchorTween = testRectTransform.DoAnchorPosition(Vector2.one * 100, testDuration);
            yield return new WaitForSeconds(testDuration + 0.1f);
            LogTestResult("DoAnchorPosition", anchorTween != null);

            // 测试尺寸动画
            var sizeTween = testRectTransform.DoSizeDelta(Vector2.one * 200, testDuration);
            yield return new WaitForSeconds(testDuration + 0.1f);
            LogTestResult("DoSizeDelta", sizeTween != null);
        }

        /// <summary>
        /// 测试 Material 动画
        /// </summary>
        private IEnumerator TestMaterialAnimations()
        {
            if (testMaterial == null) yield break;

            Debug.Log("测试 Material 动画...");

            // 测试颜色动画
            var colorTween = testMaterial.DoColor("_Color", Color.red, testDuration);
            yield return new WaitForSeconds(testDuration + 0.1f);
            LogTestResult("Material DoColor", colorTween != null);

            // 测试浮点数动画
            var floatTween = testMaterial.DoFloat("_Alpha", 0.5f, testDuration);
            yield return new WaitForSeconds(testDuration + 0.1f);
            LogTestResult("Material DoFloat", floatTween != null);
        }

        /// <summary>
        /// 测试 UI 动画
        /// </summary>
        private IEnumerator TestUIAnimations()
        {
            Debug.Log("测试 UI 动画...");

            // 测试 Graphic Alpha
            if (testGraphic != null)
            {
                var graphicAlphaTween = testGraphic.DoAlpha(0.5f, testDuration);
                yield return new WaitForSeconds(testDuration + 0.1f);
                LogTestResult("Graphic DoAlpha", graphicAlphaTween != null);
            }

            // 测试 CanvasGroup Alpha
            if (testCanvasGroup != null)
            {
                var canvasAlphaTween = testCanvasGroup.DoAlpha(0.5f, testDuration);
                yield return new WaitForSeconds(testDuration + 0.1f);
                LogTestResult("CanvasGroup DoAlpha", canvasAlphaTween != null);
            }

            // 测试 SpriteRenderer Alpha
            if (testSpriteRenderer != null)
            {
                var spriteAlphaTween = testSpriteRenderer.DoAlpha(0.5f, testDuration);
                yield return new WaitForSeconds(testDuration + 0.1f);
                LogTestResult("SpriteRenderer DoAlpha", spriteAlphaTween != null);
            }
        }

        /// <summary>
        /// 测试 Audio 动画
        /// </summary>
        private IEnumerator TestAudioAnimations()
        {
            if (testAudioSource == null) yield break;

            Debug.Log("测试 Audio 动画...");

            // 测试音量动画
            var volumeTween = testAudioSource.DoVolume(0.5f, testDuration);
            yield return new WaitForSeconds(testDuration + 0.1f);
            LogTestResult("DoVolume", volumeTween != null);
        }

        /// <summary>
        /// 测试链式调用
        /// </summary>
        private IEnumerator TestChainedCalls()
        {
            if (testTransform == null) yield break;

            Debug.Log("测试链式调用...");

            // 测试链式调用
            var chainedTween = testTransform.DoPosition(Vector3.zero, testDuration)
                .SetEasing(EasingType.QuadOut)
                .SetDelay(0.1f)
                .SetOnFinish(() => Debug.Log("链式调用完成"));

            yield return new WaitForSeconds(testDuration + 0.2f);
            LogTestResult("Chained Calls", chainedTween != null);
        }

        /// <summary>
        /// 测试序列动画
        /// </summary>
        private IEnumerator TestSequenceAnimations()
        {
            if (testTransform == null) yield break;

            Debug.Log("测试序列动画...");

            // 测试序列动画
            var sequence = testTransform.DoSequence()
                .Append(testTransform.DoPosition(Vector3.up, testDuration))
                .Append(testTransform.DoLocalScale(Vector3.one * 2f, testDuration))
                .Append(testTransform.DoPosition(Vector3.zero, testDuration));

            yield return new WaitForSeconds(testDuration * 3 + 0.1f);
            LogTestResult("Sequence Animation", sequence != null);
        }

        /// <summary>
        /// 记录测试结果
        /// </summary>
        /// <param name="testName">测试名称</param>
        /// <param name="passed">是否通过</param>
        private void LogTestResult(string testName, bool passed)
        {
            completedTests++;
            string result = passed ? "✓ 通过" : "✗ 失败";
            Debug.Log($"[{completedTests}/{totalTests}] {testName}: {result}");
        }

        /// <summary>
        /// 手动运行测试（供编辑器调用）
        /// </summary>
        [ContextMenu("运行兼容性测试")]
        public void RunTestsManually()
        {
            StartCoroutine(RunAllCompatibilityTests());
        }

        /// <summary>
        /// 切换对象池状态
        /// </summary>
        [ContextMenu("切换对象池状态")]
        public void TogglePooling()
        {
            enablePooling = !enablePooling;
            Debug.Log($"对象池状态已切换为: {(enablePooling ? "启用" : "禁用")}");
        }
    }
}
