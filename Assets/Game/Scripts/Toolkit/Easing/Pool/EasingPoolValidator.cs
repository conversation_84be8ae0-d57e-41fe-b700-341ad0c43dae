using UnityEngine;
using System.Collections.Generic;

namespace UncleChenGames.Easing.Pool
{
    /// <summary>
    /// 缓动对象池验证器
    /// 用于检测对象池的状态和潜在问题
    /// </summary>
    public static class EasingPoolValidator
    {
        /// <summary>
        /// 验证池化对象的状态是否正确重置
        /// </summary>
        /// <param name="coroutine">要验证的池化协程</param>
        /// <returns>验证结果</returns>
        public static ValidationResult ValidatePoolableCoroutine(IPoolableEasingCoroutine coroutine)
        {
            var result = new ValidationResult();

            if (coroutine == null)
            {
                result.AddError("协程对象为空");
                return result;
            }

            // 检查基本状态
            if (coroutine.IsActive)
            {
                result.AddWarning("协程仍处于活跃状态，可能未正确停止");
            }

            if (!coroutine.CanBePooled)
            {
                result.AddError("协程不能被池化，状态异常");
            }

            // 检查持续时间
            if (coroutine.Duration < 0)
            {
                result.AddError($"持续时间异常: {coroutine.Duration}");
            }

            return result;
        }

        /// <summary>
        /// 验证对象池管理器的状态
        /// </summary>
        /// <returns>验证结果</returns>
        public static ValidationResult ValidatePoolManager()
        {
            var result = new ValidationResult();

            // 检查管理器是否存在
            if (EasingPoolManager.Instance == null)
            {
                result.AddError("EasingPoolManager 实例不存在");
                return result;
            }

            // 检查对象池设置
            if (!EasingManager.UseObjectPool)
            {
                result.AddWarning("对象池功能已禁用");
            }

            result.AddInfo("对象池管理器状态正常");
            return result;
        }

        /// <summary>
        /// 检测内存泄漏风险
        /// </summary>
        /// <returns>检测结果</returns>
        public static ValidationResult CheckMemoryLeaks()
        {
            var result = new ValidationResult();

            // 检查是否有过多的活跃协程
            // 这里可以添加更多的内存泄漏检测逻辑
            
            result.AddInfo("内存泄漏检测完成");
            return result;
        }
    }

    /// <summary>
    /// 验证结果类
    /// </summary>
    public class ValidationResult
    {
        private List<string> errors = new List<string>();
        private List<string> warnings = new List<string>();
        private List<string> infos = new List<string>();

        /// <summary>
        /// 是否有错误
        /// </summary>
        public bool HasErrors => errors.Count > 0;

        /// <summary>
        /// 是否有警告
        /// </summary>
        public bool HasWarnings => warnings.Count > 0;

        /// <summary>
        /// 错误数量
        /// </summary>
        public int ErrorCount => errors.Count;

        /// <summary>
        /// 警告数量
        /// </summary>
        public int WarningCount => warnings.Count;

        /// <summary>
        /// 添加错误
        /// </summary>
        /// <param name="message">错误信息</param>
        public void AddError(string message)
        {
            errors.Add(message);
            Debug.LogError($"[EasingPool] 错误: {message}");
        }

        /// <summary>
        /// 添加警告
        /// </summary>
        /// <param name="message">警告信息</param>
        public void AddWarning(string message)
        {
            warnings.Add(message);
            Debug.LogWarning($"[EasingPool] 警告: {message}");
        }

        /// <summary>
        /// 添加信息
        /// </summary>
        /// <param name="message">信息内容</param>
        public void AddInfo(string message)
        {
            infos.Add(message);
            Debug.Log($"[EasingPool] 信息: {message}");
        }

        /// <summary>
        /// 获取所有错误信息
        /// </summary>
        /// <returns>错误信息列表</returns>
        public List<string> GetErrors()
        {
            return new List<string>(errors);
        }

        /// <summary>
        /// 获取所有警告信息
        /// </summary>
        /// <returns>警告信息列表</returns>
        public List<string> GetWarnings()
        {
            return new List<string>(warnings);
        }

        /// <summary>
        /// 获取所有信息
        /// </summary>
        /// <returns>信息列表</returns>
        public List<string> GetInfos()
        {
            return new List<string>(infos);
        }

        /// <summary>
        /// 获取完整的验证报告
        /// </summary>
        /// <returns>验证报告字符串</returns>
        public string GetReport()
        {
            var report = "=== 缓动对象池验证报告 ===\n";
            
            if (errors.Count > 0)
            {
                report += $"\n错误 ({errors.Count}):\n";
                foreach (var error in errors)
                {
                    report += $"  - {error}\n";
                }
            }

            if (warnings.Count > 0)
            {
                report += $"\n警告 ({warnings.Count}):\n";
                foreach (var warning in warnings)
                {
                    report += $"  - {warning}\n";
                }
            }

            if (infos.Count > 0)
            {
                report += $"\n信息 ({infos.Count}):\n";
                foreach (var info in infos)
                {
                    report += $"  - {info}\n";
                }
            }

            return report;
        }

        /// <summary>
        /// 清空所有结果
        /// </summary>
        public void Clear()
        {
            errors.Clear();
            warnings.Clear();
            infos.Clear();
        }
    }
}
