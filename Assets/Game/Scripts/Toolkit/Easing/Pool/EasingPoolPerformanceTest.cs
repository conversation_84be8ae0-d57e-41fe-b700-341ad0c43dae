using UnityEngine;
using System.Collections;
using System.Collections.Generic;
using System.Diagnostics;
using UnityEngine.Profiling;

namespace UncleChenGames.Easing.Pool
{
    /// <summary>
    /// 缓动对象池性能测试
    /// 测试对象池相对于直接创建对象的性能收益
    /// </summary>
    public class EasingPoolPerformanceTest : MonoBehaviour
    {
        [Header("测试配置")]
        [SerializeField] private int testIterations = 1000; // 测试迭代次数
        [SerializeField] private float animationDuration = 0.1f; // 动画持续时间
        [SerializeField] private bool runTestsOnStart = false; // 是否在开始时运行测试
        [SerializeField] private bool logDetailedResults = true; // 是否记录详细结果

        [Header("测试对象")]
        [SerializeField] private Transform[] testTransforms; // 测试用的 Transform 数组

        private List<PerformanceTestResult> testResults = new List<PerformanceTestResult>();

        /// <summary>
        /// 性能测试结果
        /// </summary>
        [System.Serializable]
        public class PerformanceTestResult
        {
            public string testName; // 测试名称
            public bool useObjectPool; // 是否使用对象池
            public int iterations; // 迭代次数
            public long executionTimeMs; // 执行时间（毫秒）
            public long memoryAllocated; // 内存分配（字节）
            public int gcCollections; // GC 回收次数
            public float averageTimePerIteration; // 每次迭代平均时间

            public PerformanceTestResult(string name, bool pooled, int iter)
            {
                testName = name;
                useObjectPool = pooled;
                iterations = iter;
            }

            public override string ToString()
            {
                return $"{testName} ({(useObjectPool ? "池化" : "直接创建")}): " +
                       $"{executionTimeMs}ms, {memoryAllocated / 1024}KB, {gcCollections} GC, " +
                       $"{averageTimePerIteration:F3}ms/iter";
            }
        }

        /// <summary>
        /// 开始时运行测试
        /// </summary>
        private void Start()
        {
            if (runTestsOnStart)
            {
                StartCoroutine(RunPerformanceTests());
            }
        }

        /// <summary>
        /// 运行所有性能测试
        /// </summary>
        /// <returns>测试协程</returns>
        public IEnumerator RunPerformanceTests()
        {
            UnityEngine.Debug.Log("=== 开始缓动对象池性能测试 ===");
            testResults.Clear();

            // 等待一帧确保环境稳定
            yield return null;

            // 强制垃圾回收，确保测试环境干净
            System.GC.Collect();
            yield return new WaitForSeconds(0.1f);

            // 测试 Float 动画性能
            yield return StartCoroutine(TestFloatAnimationPerformance());

            // 测试 Vector3 动画性能
            yield return StartCoroutine(TestVector3AnimationPerformance());

            // 测试 Color 动画性能
            yield return StartCoroutine(TestColorAnimationPerformance());

            // 测试混合动画性能
            yield return StartCoroutine(TestMixedAnimationPerformance());

            // 输出测试结果
            OutputTestResults();

            UnityEngine.Debug.Log("=== 性能测试完成 ===");
        }

        /// <summary>
        /// 测试 Float 动画性能
        /// </summary>
        private IEnumerator TestFloatAnimationPerformance()
        {
            UnityEngine.Debug.Log("测试 Float 动画性能...");

            // 测试不使用对象池
            yield return StartCoroutine(TestFloatAnimations(false));

            // 等待一段时间让动画完成
            yield return new WaitForSeconds(animationDuration + 0.1f);

            // 测试使用对象池
            yield return StartCoroutine(TestFloatAnimations(true));

            // 等待一段时间让动画完成
            yield return new WaitForSeconds(animationDuration + 0.1f);
        }

        /// <summary>
        /// 测试 Float 动画
        /// </summary>
        /// <param name="usePool">是否使用对象池</param>
        private IEnumerator TestFloatAnimations(bool usePool)
        {
            var result = new PerformanceTestResult("Float Animation", usePool, testIterations);
            
            // 设置对象池状态
            SetPoolingState(usePool);

            // 开始性能监控
            var stopwatch = Stopwatch.StartNew();
            long startMemory = Profiler.GetTotalAllocatedMemory(false);
            int startGC = System.GC.CollectionCount(0);

            // 执行测试
            for (int i = 0; i < testIterations; i++)
            {
                float startValue = 0f;
                float targetValue = 1f;
                
                EasingManager.DoFloat(startValue, targetValue, animationDuration, (value) => {
                    // 模拟值更新操作
                });

                // 每100次迭代让出一帧，避免卡顿
                if (i % 100 == 0)
                {
                    yield return null;
                }
            }

            // 结束性能监控
            stopwatch.Stop();
            long endMemory = Profiler.GetTotalAllocatedMemory(false);
            int endGC = System.GC.CollectionCount(0);

            // 记录结果
            result.executionTimeMs = stopwatch.ElapsedMilliseconds;
            result.memoryAllocated = endMemory - startMemory;
            result.gcCollections = endGC - startGC;
            result.averageTimePerIteration = (float)result.executionTimeMs / testIterations;

            testResults.Add(result);

            if (logDetailedResults)
            {
                UnityEngine.Debug.Log(result.ToString());
            }
        }

        /// <summary>
        /// 测试 Vector3 动画性能
        /// </summary>
        private IEnumerator TestVector3AnimationPerformance()
        {
            UnityEngine.Debug.Log("测试 Vector3 动画性能...");

            if (testTransforms == null || testTransforms.Length == 0)
            {
                UnityEngine.Debug.LogWarning("没有可用的测试 Transform，跳过 Vector3 测试");
                yield break;
            }

            // 测试不使用对象池
            yield return StartCoroutine(TestVector3Animations(false));
            yield return new WaitForSeconds(animationDuration + 0.1f);

            // 测试使用对象池
            yield return StartCoroutine(TestVector3Animations(true));
            yield return new WaitForSeconds(animationDuration + 0.1f);
        }

        /// <summary>
        /// 测试 Vector3 动画
        /// </summary>
        /// <param name="usePool">是否使用对象池</param>
        private IEnumerator TestVector3Animations(bool usePool)
        {
            var result = new PerformanceTestResult("Vector3 Animation", usePool, testIterations);
            
            SetPoolingState(usePool);

            var stopwatch = Stopwatch.StartNew();
            long startMemory = Profiler.GetTotalAllocatedMemory(false);
            int startGC = System.GC.CollectionCount(0);

            for (int i = 0; i < testIterations; i++)
            {
                var transform = testTransforms[i % testTransforms.Length];
                if (transform != null)
                {
                    Vector3 targetPosition = new Vector3(
                        Random.Range(-5f, 5f),
                        Random.Range(-5f, 5f),
                        Random.Range(-5f, 5f)
                    );
                    
                    transform.DoPosition(targetPosition, animationDuration);
                }

                if (i % 100 == 0)
                {
                    yield return null;
                }
            }

            stopwatch.Stop();
            long endMemory = Profiler.GetTotalAllocatedMemory(false);
            int endGC = System.GC.CollectionCount(0);

            result.executionTimeMs = stopwatch.ElapsedMilliseconds;
            result.memoryAllocated = endMemory - startMemory;
            result.gcCollections = endGC - startGC;
            result.averageTimePerIteration = (float)result.executionTimeMs / testIterations;

            testResults.Add(result);

            if (logDetailedResults)
            {
                UnityEngine.Debug.Log(result.ToString());
            }
        }

        /// <summary>
        /// 测试 Color 动画性能
        /// </summary>
        private IEnumerator TestColorAnimationPerformance()
        {
            UnityEngine.Debug.Log("测试 Color 动画性能...");

            // 测试不使用对象池
            yield return StartCoroutine(TestColorAnimations(false));
            yield return new WaitForSeconds(animationDuration + 0.1f);

            // 测试使用对象池
            yield return StartCoroutine(TestColorAnimations(true));
            yield return new WaitForSeconds(animationDuration + 0.1f);
        }

        /// <summary>
        /// 测试 Color 动画
        /// </summary>
        /// <param name="usePool">是否使用对象池</param>
        private IEnumerator TestColorAnimations(bool usePool)
        {
            var result = new PerformanceTestResult("Color Animation", usePool, testIterations);
            
            SetPoolingState(usePool);

            var stopwatch = Stopwatch.StartNew();
            long startMemory = Profiler.GetTotalAllocatedMemory(false);
            int startGC = System.GC.CollectionCount(0);

            for (int i = 0; i < testIterations; i++)
            {
                Color startColor = Color.white;
                Color targetColor = new Color(
                    Random.Range(0f, 1f),
                    Random.Range(0f, 1f),
                    Random.Range(0f, 1f),
                    1f
                );

                // 使用 EasingPoolManager 直接创建 Color 动画
                if (usePool)
                {
                    EasingPoolManager.GetColorCoroutine(startColor, targetColor, animationDuration, 0f, (color) => {
                        // 模拟颜色更新操作
                    });
                }
                else
                {
                    new ColorEasingCoroutine(startColor, targetColor, animationDuration, 0f, (color) => {
                        // 模拟颜色更新操作
                    });
                }

                if (i % 100 == 0)
                {
                    yield return null;
                }
            }

            stopwatch.Stop();
            long endMemory = Profiler.GetTotalAllocatedMemory(false);
            int endGC = System.GC.CollectionCount(0);

            result.executionTimeMs = stopwatch.ElapsedMilliseconds;
            result.memoryAllocated = endMemory - startMemory;
            result.gcCollections = endGC - startGC;
            result.averageTimePerIteration = (float)result.executionTimeMs / testIterations;

            testResults.Add(result);

            if (logDetailedResults)
            {
                UnityEngine.Debug.Log(result.ToString());
            }
        }

        /// <summary>
        /// 测试混合动画性能
        /// </summary>
        private IEnumerator TestMixedAnimationPerformance()
        {
            UnityEngine.Debug.Log("测试混合动画性能...");

            // 测试不使用对象池
            yield return StartCoroutine(TestMixedAnimations(false));
            yield return new WaitForSeconds(animationDuration + 0.1f);

            // 测试使用对象池
            yield return StartCoroutine(TestMixedAnimations(true));
            yield return new WaitForSeconds(animationDuration + 0.1f);
        }

        /// <summary>
        /// 测试混合动画
        /// </summary>
        /// <param name="usePool">是否使用对象池</param>
        private IEnumerator TestMixedAnimations(bool usePool)
        {
            var result = new PerformanceTestResult("Mixed Animation", usePool, testIterations);
            
            SetPoolingState(usePool);

            var stopwatch = Stopwatch.StartNew();
            long startMemory = Profiler.GetTotalAllocatedMemory(false);
            int startGC = System.GC.CollectionCount(0);

            for (int i = 0; i < testIterations; i++)
            {
                // 随机选择动画类型
                int animType = i % 4;
                
                switch (animType)
                {
                    case 0: // Float
                        EasingManager.DoFloat(0f, 1f, animationDuration, (value) => { });
                        break;
                    case 1: // Vector3
                        if (testTransforms != null && testTransforms.Length > 0)
                        {
                            var transform = testTransforms[i % testTransforms.Length];
                            if (transform != null)
                            {
                                transform.DoPosition(Vector3.zero, animationDuration);
                            }
                        }
                        break;
                    case 2: // Vector2
                        if (usePool)
                        {
                            EasingPoolManager.GetVector2Coroutine(Vector2.zero, Vector2.one, animationDuration, 0f, (value) => { });
                        }
                        else
                        {
                            new VectorEasingCoroutine2(Vector2.zero, Vector2.one, animationDuration, 0f, (value) => { });
                        }
                        break;
                    case 3: // Color
                        if (usePool)
                        {
                            EasingPoolManager.GetColorCoroutine(Color.white, Color.red, animationDuration, 0f, (value) => { });
                        }
                        else
                        {
                            new ColorEasingCoroutine(Color.white, Color.red, animationDuration, 0f, (value) => { });
                        }
                        break;
                }

                if (i % 100 == 0)
                {
                    yield return null;
                }
            }

            stopwatch.Stop();
            long endMemory = Profiler.GetTotalAllocatedMemory(false);
            int endGC = System.GC.CollectionCount(0);

            result.executionTimeMs = stopwatch.ElapsedMilliseconds;
            result.memoryAllocated = endMemory - startMemory;
            result.gcCollections = endGC - startGC;
            result.averageTimePerIteration = (float)result.executionTimeMs / testIterations;

            testResults.Add(result);

            if (logDetailedResults)
            {
                UnityEngine.Debug.Log(result.ToString());
            }
        }

        /// <summary>
        /// 设置对象池状态
        /// </summary>
        /// <param name="usePool">是否使用对象池</param>
        private void SetPoolingState(bool usePool)
        {
            // 这里需要通过反射或其他方式设置 EasingManager 的 useObjectPool 字段
            // 由于字段是私有的，这里只是示例
            UnityEngine.Debug.Log($"设置对象池状态: {usePool}");
        }

        /// <summary>
        /// 输出测试结果
        /// </summary>
        private void OutputTestResults()
        {
            UnityEngine.Debug.Log("\n=== 性能测试结果汇总 ===");
            
            foreach (var result in testResults)
            {
                UnityEngine.Debug.Log(result.ToString());
            }

            // 计算性能提升
            CalculatePerformanceGains();
        }

        /// <summary>
        /// 计算性能提升
        /// </summary>
        private void CalculatePerformanceGains()
        {
            var testTypes = new HashSet<string>();
            foreach (var result in testResults)
            {
                testTypes.Add(result.testName);
            }

            UnityEngine.Debug.Log("\n=== 性能提升分析 ===");
            
            foreach (var testType in testTypes)
            {
                var pooledResult = testResults.Find(r => r.testName == testType && r.useObjectPool);
                var directResult = testResults.Find(r => r.testName == testType && !r.useObjectPool);

                if (pooledResult != null && directResult != null)
                {
                    float timeImprovement = ((float)(directResult.executionTimeMs - pooledResult.executionTimeMs) / directResult.executionTimeMs) * 100f;
                    float memoryImprovement = ((float)(directResult.memoryAllocated - pooledResult.memoryAllocated) / directResult.memoryAllocated) * 100f;
                    int gcImprovement = directResult.gcCollections - pooledResult.gcCollections;

                    UnityEngine.Debug.Log($"{testType}:");
                    UnityEngine.Debug.Log($"  时间提升: {timeImprovement:F1}%");
                    UnityEngine.Debug.Log($"  内存节省: {memoryImprovement:F1}%");
                    UnityEngine.Debug.Log($"  GC减少: {gcImprovement} 次");
                }
            }
        }

        /// <summary>
        /// 手动运行性能测试
        /// </summary>
        [ContextMenu("运行性能测试")]
        public void RunTestsManually()
        {
            StartCoroutine(RunPerformanceTests());
        }

        /// <summary>
        /// 清空测试结果
        /// </summary>
        [ContextMenu("清空测试结果")]
        public void ClearResults()
        {
            testResults.Clear();
            UnityEngine.Debug.Log("测试结果已清空");
        }
    }
}
