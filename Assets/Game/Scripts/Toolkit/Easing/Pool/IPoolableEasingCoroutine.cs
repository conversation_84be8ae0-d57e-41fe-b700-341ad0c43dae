using UnityEngine;
using UnityEngine.Events;

namespace UncleChenGames.Easing.Pool
{
    /// <summary>
    /// 可池化的缓动协程接口
    /// 定义池化对象的重置和初始化方法
    /// </summary>
    public interface IPoolableEasingCoroutine : IEasingCoroutine
    {
        /// <summary>
        /// 重置对象状态，准备重新使用
        /// </summary>
        void Reset();

        /// <summary>
        /// 检查对象是否可以被回收到池中
        /// </summary>
        bool CanBePooled { get; }

        /// <summary>
        /// 对象完成时的回收回调
        /// </summary>
        System.Action<IPoolableEasingCoroutine> OnPoolReturn { get; set; }
    }

    /// <summary>
    /// 可池化的泛型缓动协程接口
    /// </summary>
    /// <typeparam name="T">缓动值类型</typeparam>
    public interface IPoolableEasingCoroutine<T> : IPoolableEasingCoroutine
    {
        /// <summary>
        /// 初始化缓动参数
        /// </summary>
        /// <param name="from">起始值</param>
        /// <param name="to">目标值</param>
        /// <param name="duration">持续时间</param>
        /// <param name="delay">延迟时间</param>
        /// <param name="callback">值更新回调</param>
        void Initialize(T from, T to, float duration, float delay, UnityAction<T> callback);
    }
}
