using System;
using System.Collections;
using System.Runtime.CompilerServices;
using UnityEngine;
using UnityEngine.Events;

namespace UncleChenGames.Easing.Pool
{
    /// <summary>
    /// 可池化的空协程基类
    /// 为所有可池化的缓动协程提供通用功能
    /// </summary>
    public abstract class PoolableEmptyCoroutine : IPoolableEasingCoroutine
    {
        protected Coroutine coroutine; // Unity协程实例
        protected UnityAction finishCallback; // 完成时的回调函数
        protected EasingType easingType = EasingType.Linear; // 缓动类型
        protected float delay = 0f; // 延迟时间
        protected bool unscaledTime; // 是否使用不受缩放影响的时间
        protected bool useCurve; // 是否使用动画曲线
        protected AnimationCurve easingCurve; // 动画曲线

        public bool IsActive { get; protected set; } // 是否正在活跃
        public virtual float Duration { get; protected set; } // 持续时间
        public bool CanBePooled => !IsActive; // 是否可以被池化
        public Action<IPoolableEasingCoroutine> OnPoolReturn { get; set; } // 回收回调

        /// <summary>
        /// 重置对象状态
        /// </summary>
        public virtual void Reset()
        {
            // 停止协程
            if (coroutine != null && IsActive)
            {
                EasingManager.StopCustomCoroutine(coroutine);
            }

            // 重置所有状态
            coroutine = null;
            IsActive = false;
            Duration = 0f;
            finishCallback = null;
            easingType = EasingType.Linear;
            delay = 0f;
            unscaledTime = false;
            useCurve = false;
            easingCurve = null;

            // 清理回收回调，避免循环引用
            OnPoolReturn = null;
        }

        public IEasingCoroutine SetEasing(EasingType easingType)
        {
            this.easingType = easingType;
            useCurve = false;
            return this;
        }

        public IEasingCoroutine SetOnFinish(UnityAction callback)
        {
            finishCallback = callback;
            return this;
        }

        public IEasingCoroutine SetUnscaledTime(bool unscaledTime)
        {
            this.unscaledTime = unscaledTime;
            return this;
        }

        public IEasingCoroutine SetEasingCurve(AnimationCurve curve)
        {
            easingCurve = curve;
            useCurve = true;
            return this;
        }

        public IEasingCoroutine SetDelay(float delay)
        {
            this.delay = delay;
            return this;
        }

        public void Stop()
        {
            if (coroutine != null)
            {
                EasingManager.StopCustomCoroutine(coroutine);
            }
            IsActive = false;
            
            // 通知池回收对象
            OnPoolReturn?.Invoke(this);
        }

        /// <summary>
        /// 协程完成时调用
        /// </summary>
        protected virtual void OnCoroutineComplete()
        {
            IsActive = false;
            finishCallback?.Invoke();
            
            // 通知池回收对象
            OnPoolReturn?.Invoke(this);
        }
    }

    /// <summary>
    /// 可池化的泛型缓动协程基类
    /// </summary>
    /// <typeparam name="T">缓动值类型</typeparam>
    public abstract class PoolableEasingCoroutine<T> : PoolableEmptyCoroutine, IPoolableEasingCoroutine<T>
    {
        protected T from; // 起始值
        protected T to; // 目标值
        protected UnityAction<T> callback; // 值更新回调

        /// <summary>
        /// 抽象的插值方法
        /// </summary>
        /// <param name="a">起始值</param>
        /// <param name="b">目标值</param>
        /// <param name="t">插值参数</param>
        /// <returns>插值结果</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public abstract T Lerp(T a, T b, float t);

        /// <summary>
        /// 初始化缓动参数
        /// </summary>
        /// <param name="from">起始值</param>
        /// <param name="to">目标值</param>
        /// <param name="duration">持续时间</param>
        /// <param name="delay">延迟时间</param>
        /// <param name="callback">值更新回调</param>
        public void Initialize(T from, T to, float duration, float delay, UnityAction<T> callback)
        {
            // 验证参数
            if (duration < 0)
            {
                Debug.LogWarning($"缓动持续时间不能为负数: {duration}，已设置为0");
                duration = 0;
            }

            if (callback == null)
            {
                Debug.LogWarning("缓动回调函数为空，动画将不会产生可见效果");
            }

            this.from = from;
            this.to = to;
            this.Duration = duration;
            this.delay = delay;
            this.callback = callback;

            // 启动协程
            coroutine = EasingManager.StartCustomCoroutine(EasingCoroutine());
        }

        /// <summary>
        /// 重置对象状态
        /// </summary>
        public override void Reset()
        {
            base.Reset();
            
            // 重置泛型相关状态
            from = default(T);
            to = default(T);
            callback = null;
        }

        /// <summary>
        /// 缓动协程的具体实现
        /// </summary>
        /// <returns>协程迭代器</returns>
        private IEnumerator EasingCoroutine()
        {
            IsActive = true;
            float time = 0;

            // 处理延迟
            while (delay > 0)
            {
                yield return null;
                delay -= unscaledTime ? Time.unscaledDeltaTime : Time.deltaTime;
            }

            // 执行缓动
            while (time < Duration)
            {
                yield return null;

                time += unscaledTime ? Time.unscaledDeltaTime : Time.deltaTime;
                float t;
                
                if (useCurve)
                {
                    t = easingCurve.Evaluate(time / Duration);
                }
                else
                {
                    t = EasingFunctions.ApplyEasing(time / Duration, easingType);
                }

                T value = Lerp(from, to, t);
                callback?.Invoke(value);
            }

            // 确保最终值正确
            callback?.Invoke(to);
            
            // 协程完成
            OnCoroutineComplete();
        }
    }

    /// <summary>
    /// 可池化的浮点数缓动协程
    /// </summary>
    public class PoolableFloatEasingCoroutine : PoolableEasingCoroutine<float>
    {
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public override float Lerp(float a, float b, float t)
        {
            return Mathf.LerpUnclamped(a, b, t);
        }
    }

    /// <summary>
    /// 可池化的 Vector3 缓动协程
    /// </summary>
    public class PoolableVector3EasingCoroutine : PoolableEasingCoroutine<Vector3>
    {
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public override Vector3 Lerp(Vector3 a, Vector3 b, float t)
        {
            return Vector3.LerpUnclamped(a, b, t);
        }
    }

    /// <summary>
    /// 可池化的 Vector2 缓动协程
    /// </summary>
    public class PoolableVector2EasingCoroutine : PoolableEasingCoroutine<Vector2>
    {
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public override Vector2 Lerp(Vector2 a, Vector2 b, float t)
        {
            return Vector2.LerpUnclamped(a, b, t);
        }
    }

    /// <summary>
    /// 可池化的 Color 缓动协程
    /// </summary>
    public class PoolableColorEasingCoroutine : PoolableEasingCoroutine<Color>
    {
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public override Color Lerp(Color a, Color b, float t)
        {
            return Color.LerpUnclamped(a, b, t);
        }
    }
}
