using System;
using UnityEngine;
using UnityEngine.Events;
using UncleChenGames.Pool;

namespace UncleChenGames.Easing.Pool
{
    /// <summary>
    /// 缓动协程对象池基类
    /// 为各种类型的缓动协程提供池化管理
    /// </summary>
    /// <typeparam name="T">缓动值类型</typeparam>
    /// <typeparam name="TCoroutine">具体的缓动协程类型</typeparam>
    public abstract class EasingCoroutinePool<T, TCoroutine> : Pool<TCoroutine>
        where TCoroutine : class, IPoolableEasingCoroutine<T>, new()
    {
        private const int DEFAULT_POOL_SIZE = 10; // 默认池大小
        private const int MAX_POOL_SIZE = 100;    // 最大池大小

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="poolName">对象池名称</param>
        /// <param name="initialSize">初始大小</param>
        public EasingCoroutinePool(string poolName, int initialSize = DEFAULT_POOL_SIZE) 
            : base(poolName, initialSize)
        {
            // 预创建指定数量的对象
            for (int i = 0; i < initialSize; i++)
            {
                AddNewEntity();
            }
        }

        /// <summary>
        /// 创建新的缓动协程实体
        /// </summary>
        /// <returns>新创建的缓动协程</returns>
        protected override TCoroutine CreateEntity()
        {
            var entity = new TCoroutine();
            return entity;
        }

        /// <summary>
        /// 初始化缓动协程实体
        /// </summary>
        /// <param name="entity">要初始化的实体</param>
        protected override void InitEntity(TCoroutine entity)
        {
            // 设置回收回调
            entity.OnPoolReturn = ReturnToPool;
            // 重置状态
            entity.Reset();
        }

        /// <summary>
        /// 验证缓动协程实体是否可用
        /// </summary>
        /// <param name="entity">要验证的实体</param>
        /// <returns>是否可用</returns>
        protected override bool ValidateEntity(TCoroutine entity)
        {
            return entity != null && entity.CanBePooled && !entity.IsActive;
        }

        /// <summary>
        /// 禁用缓动协程实体
        /// </summary>
        /// <param name="entity">要禁用的实体</param>
        protected override void DisableEntity(TCoroutine entity)
        {
            if (entity != null && entity.IsActive)
            {
                entity.Stop();
            }
        }

        /// <summary>
        /// 销毁缓动协程实体
        /// </summary>
        /// <param name="entity">要销毁的实体</param>
        protected override void DestroyEntity(TCoroutine entity)
        {
            // 对于缓动协程，销毁就是停止并清理
            if (entity != null)
            {
                entity.Stop();
                entity.Reset();
            }
        }

        /// <summary>
        /// 获取并初始化缓动协程
        /// </summary>
        /// <param name="from">起始值</param>
        /// <param name="to">目标值</param>
        /// <param name="duration">持续时间</param>
        /// <param name="delay">延迟时间</param>
        /// <param name="callback">值更新回调</param>
        /// <returns>初始化后的缓动协程</returns>
        public TCoroutine GetCoroutine(T from, T to, float duration, float delay, UnityAction<T> callback)
        {
            var coroutine = GetEntity();
            coroutine.Initialize(from, to, duration, delay, callback);
            return coroutine;
        }

        /// <summary>
        /// 将对象返回到池中
        /// </summary>
        /// <param name="coroutine">要返回的缓动协程</param>
        private void ReturnToPool(IPoolableEasingCoroutine coroutine)
        {
            if (coroutine is TCoroutine typedCoroutine && typedCoroutine.CanBePooled)
            {
                typedCoroutine.Reset();
                // 对象已经在池中，无需额外操作
            }
        }
    }

    /// <summary>
    /// 浮点数缓动协程对象池
    /// </summary>
    public class FloatEasingCoroutinePool : EasingCoroutinePool<float, PoolableFloatEasingCoroutine>
    {
        public FloatEasingCoroutinePool(int initialSize = 10) 
            : base("FloatEasingCoroutine", initialSize)
        {
        }
    }

    /// <summary>
    /// Vector3 缓动协程对象池
    /// </summary>
    public class Vector3EasingCoroutinePool : EasingCoroutinePool<Vector3, PoolableVector3EasingCoroutine>
    {
        public Vector3EasingCoroutinePool(int initialSize = 10) 
            : base("Vector3EasingCoroutine", initialSize)
        {
        }
    }

    /// <summary>
    /// Vector2 缓动协程对象池
    /// </summary>
    public class Vector2EasingCoroutinePool : EasingCoroutinePool<Vector2, PoolableVector2EasingCoroutine>
    {
        public Vector2EasingCoroutinePool(int initialSize = 10) 
            : base("Vector2EasingCoroutine", initialSize)
        {
        }
    }

    /// <summary>
    /// Color 缓动协程对象池
    /// </summary>
    public class ColorEasingCoroutinePool : EasingCoroutinePool<Color, PoolableColorEasingCoroutine>
    {
        public ColorEasingCoroutinePool(int initialSize = 10) 
            : base("ColorEasingCoroutine", initialSize)
        {
        }
    }
}
