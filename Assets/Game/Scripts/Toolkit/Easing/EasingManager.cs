using System;
using System.Collections;
using System.Runtime.CompilerServices;
using UnityEngine;
using UnityEngine.Events;
using UncleChenGames.Easing.Pool;

namespace UncleChenGames.Easing
{
    /// <summary>
    /// 缓动管理器
    /// 提供各种缓动动画的创建和管理功能
    /// </summary>
    public class EasingManager : MonoBehaviour
    {
        private static EasingManager instance; // 单例实例

        [Header("对象池设置")]
        [SerializeField] private bool useObjectPool = true; // 是否使用对象池

        /// <summary>
        /// 是否启用对象池
        /// </summary>
        public static bool UseObjectPool => instance != null ? instance.useObjectPool : true;

        /// <summary>
        /// 初始化缓动管理器
        /// </summary>
        public void Awake()
        {
            // 检查是否已存在实例
            if (instance != null && instance != this)
            {
                Debug.LogError("检测到多个 EasingManager 实例！正在销毁重复的实例。");
                Destroy(gameObject);
                return;
            }

            // 设置单例实例
            instance = this;
        }

        /// <summary>
        /// 创建浮点数缓动
        /// </summary>
        /// <param name="from">起始值</param>
        /// <param name="to">目标值</param>
        /// <param name="duration">持续时间</param>
        /// <param name="action">值更新回调</param>
        /// <param name="delay">延迟时间</param>
        /// <returns>缓动协程接口</returns>
        public static IEasingCoroutine DoFloat(float from, float to, float duration, UnityAction<float> action, float delay = 0)
        {
            // 根据设置选择使用对象池或直接创建
            if (UseObjectPool)
            {
                return EasingPoolManager.GetFloatCoroutine(from, to, duration, delay, action);
            }
            else
            {
                return new FloatEasingCoroutine(from, to, duration, delay, action);
            }
        }

        public static IEasingCoroutine DoAfter(float seconds, UnityAction action, bool unscaledTime = false)
        {
            return new WaitCoroutine(seconds, unscaledTime).SetOnFinish(action);
        }

        public static IEasingCoroutine DoAfter(Func<bool> condition)
        {
            return new WaitForConditionCoroutine(condition);
        }

        public static IEasingCoroutine DoNextFrame()
        {
            return new NextFrameCoroutine();
        }
        public static IEasingCoroutine DoNextFrame(UnityAction action)
        {
            return new NextFrameCoroutine().SetOnFinish(action);
        }

        public static IEasingCoroutine DoNextFixedFrame()
        {
            return new NextFixedFrameCoroutine();
        }

        public static Coroutine StartCustomCoroutine(IEnumerator coroutine)
        {
            return instance.StartCoroutine(coroutine);
        }

        public static void StopCustomCoroutine(Coroutine coroutine)
        {
            if (instance != null) instance.StopCoroutine(coroutine);
        }

        private void OnDestroy()
        {
            // 清空单例实例引用
            if (instance == this) instance = null;
        }
    }

    /// <summary>
    /// 缓动协程接口
    /// 定义缓动动画的基本操作和属性
    /// </summary>
    public interface IEasingCoroutine
    {
        bool IsActive { get; } // 是否正在活跃

        float Duration { get; } // 持续时间

        /// <summary>
        /// 设置缓动类型
        /// </summary>
        /// <param name="easingType">缓动类型</param>
        /// <returns>缓动协程接口</returns>
        IEasingCoroutine SetEasing(EasingType easingType);

        /// <summary>
        /// 设置缓动曲线
        /// </summary>
        /// <param name="easingCurve">动画曲线</param>
        /// <returns>缓动协程接口</returns>
        IEasingCoroutine SetEasingCurve(AnimationCurve easingCurve);

        /// <summary>
        /// 设置完成回调
        /// </summary>
        /// <param name="callback">回调函数</param>
        /// <returns>缓动协程接口</returns>
        IEasingCoroutine SetOnFinish(UnityAction callback);

        /// <summary>
        /// 设置是否使用不受时间缩放影响的时间
        /// </summary>
        /// <param name="unscaledTime">是否使用不受缩放影响的时间</param>
        /// <returns>缓动协程接口</returns>
        IEasingCoroutine SetUnscaledTime(bool unscaledTime);

        /// <summary>
        /// 设置延迟时间
        /// </summary>
        /// <param name="delay">延迟时间</param>
        /// <returns>缓动协程接口</returns>
        IEasingCoroutine SetDelay(float delay);

        /// <summary>
        /// 停止缓动
        /// </summary>
        void Stop();
    }

    /// <summary>
    /// 空协程基类
    /// 为所有缓动协程提供通用功能和属性
    /// </summary>
    public abstract class EmptyCoroutine : IEasingCoroutine
    {
        protected Coroutine coroutine; // Unity协程实例

        public bool IsActive { get; protected set; } // 是否正在活跃

        public virtual float Duration { get; protected set; } // 持续时间

        protected UnityAction finishCallback; // 完成时的回调函数

        protected EasingType easingType = EasingType.Linear; // 缓动类型

        protected float delay = -1; // 延迟时间

        protected bool unscaledTime; // 是否使用不受缩放影响的时间

        protected bool useCurve; // 是否使用动画曲线

        protected AnimationCurve easingCurve; // 动画曲线

        public IEasingCoroutine SetEasing(EasingType easingType)
        {
            this.easingType = easingType;
            useCurve = false;
            return this;
        }

        public IEasingCoroutine SetOnFinish(UnityAction callback)
        {
            finishCallback = callback;
            return this;
        }

        public IEasingCoroutine SetUnscaledTime(bool unscaledTime)
        {
            this.unscaledTime = unscaledTime;
            return this;
        }

        public IEasingCoroutine SetEasingCurve(AnimationCurve curve)
        {
            easingCurve = curve;
            useCurve = true;

            return this;
        }

        public IEasingCoroutine SetDelay(float delay)
        {
            this.delay = delay;

            return this;
        }

        public void Stop()
        {
            EasingManager.StopCustomCoroutine(coroutine);

            IsActive = false;
        }
    }

    public class NextFrameCoroutine : EmptyCoroutine
    {
        public NextFrameCoroutine()
        {
            Duration = 0f; // 一帧的时间，实际值取决于帧率
            coroutine = EasingManager.StartCustomCoroutine(Coroutine());
        }

        private IEnumerator Coroutine()
        {
            IsActive = true;

            yield return null;

            finishCallback?.Invoke();

            IsActive = false;
        }
    }

    public class NextFixedFrameCoroutine : EmptyCoroutine
    {
        public NextFixedFrameCoroutine()
        {
            Duration = Time.fixedDeltaTime; // 固定更新的时间间隔
            coroutine = EasingManager.StartCustomCoroutine(Coroutine());
        }

        private IEnumerator Coroutine()
        {
            IsActive = true;

            yield return new WaitForFixedUpdate();

            finishCallback?.Invoke();

            IsActive = false;
        }
    }

    public class WaitCoroutine : EmptyCoroutine
    {
        public WaitCoroutine(float duration, bool unscaledTime = false)
        {
            this.Duration = duration;
            this.unscaledTime = unscaledTime;

            coroutine = EasingManager.StartCustomCoroutine(Coroutine());
        }

        private IEnumerator Coroutine()
        {
            IsActive = true;

            while (delay > 0)
            {
                yield return null;

                delay -= unscaledTime ? Time.unscaledDeltaTime : Time.deltaTime;
            }

            if (unscaledTime)
            {
                yield return new WaitForSecondsRealtime(Duration);
            }
            else
            {
                yield return new WaitForSeconds(Duration);
            }

            finishCallback?.Invoke();

            IsActive = false;
        }
    }

    public class WaitForConditionCoroutine : EmptyCoroutine
    {
        private Func<bool> condition;

        public WaitForConditionCoroutine(Func<bool> condition)
        {
            this.condition = condition;
            Duration = -1f; // 未知持续时间
            coroutine = EasingManager.StartCustomCoroutine(Coroutine());
        }

        private IEnumerator Coroutine()
        {
            IsActive = true;

            while (delay > 0)
            {
                yield return null;

                delay -= unscaledTime ? Time.unscaledDeltaTime : Time.deltaTime;
            }

            do
            {
                yield return null;
            } while (!condition());

            finishCallback?.Invoke();

            IsActive = false;
        }
    }

    public abstract class EasingCoroutine<T> : EmptyCoroutine
    {
        protected T from;
        protected T to;

        protected UnityAction<T> callback;

        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public abstract T Lerp(T a, T b, float t);

        public EasingCoroutine(T from, T to, float duration, float delay, UnityAction<T> callback)
        {
            this.from = from;
            this.to = to;
            this.Duration = duration;
            this.callback = callback;
            this.delay = delay;

            coroutine = EasingManager.StartCustomCoroutine(Coroutine());
        }

        private IEnumerator Coroutine()
        {
            IsActive = true;

            float time = 0;

            while (time < Duration)
            {
                yield return null;

                if (delay > 0)
                {
                    delay -= unscaledTime ? Time.unscaledDeltaTime : Time.deltaTime;

                    if (delay > 0) continue;
                }

                time += unscaledTime ? Time.unscaledDeltaTime : Time.deltaTime;
                float t;
                if (useCurve)
                {
                    t = easingCurve.Evaluate(time / Duration);
                }
                else
                {
                    t = EasingFunctions.ApplyEasing(time / Duration, easingType);
                }

                T value = Lerp(from, to, t);
                callback?.Invoke(value);
            }

            callback.Invoke(to);
            finishCallback?.Invoke();

            IsActive = false;
        }
    }

    public class FloatEasingCoroutine : EasingCoroutine<float>
    {
        public FloatEasingCoroutine(float from, float to, float duration, float delay, UnityAction<float> callback) : base(from, to, duration, delay, callback)
        {

        }

        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public override float Lerp(float a, float b, float t)
        {
            return Mathf.LerpUnclamped(a, b, t);
        }
    }

    public class VectorEasingCoroutine3 : EasingCoroutine<Vector3>
    {
        public VectorEasingCoroutine3(Vector3 from, Vector3 to, float duration, float delay, UnityAction<Vector3> callback) : base(from, to, duration, delay, callback)
        {

        }

        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public override Vector3 Lerp(Vector3 a, Vector3 b, float t)
        {
            return Vector3.LerpUnclamped(a, b, t);
        }
    }

    public class VectorEasingCoroutine2 : EasingCoroutine<Vector2>
    {
        public VectorEasingCoroutine2(Vector2 from, Vector2 to, float duration, float delay, UnityAction<Vector2> callback) : base(from, to, duration, delay, callback)
        {

        }

        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public override Vector2 Lerp(Vector2 a, Vector2 b, float t)
        {
            return Vector2.LerpUnclamped(a, b, t);
        }
    }

    public class ColorEasingCoroutine : EasingCoroutine<Color>
    {
        public ColorEasingCoroutine(Color from, Color to, float duration, float delay, UnityAction<Color> callback) : base(from, to, duration, delay, callback)
        {

        }

        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public override Color Lerp(Color a, Color b, float t)
        {
            return Color.LerpUnclamped(a, b, t);
        }
    }
}